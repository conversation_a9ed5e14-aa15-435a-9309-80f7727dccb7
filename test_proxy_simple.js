// test_proxy_simple.js
// Simple proxy test to verify Oxylabs credentials

import https from 'https';
import { HttpsProxyAgent } from 'https-proxy-agent';

async function testProxy() {
  console.log('🔗 Testing Oxylabs proxy credentials...');

  // Proxy configuration - using format from Endpoints.json
  const username = 'customer-MVPLEE_P3m9i-sessid-0063358822-sesstime-10';
  const password = 'p5xUcKMH7hJ4f29!!';
  const proxyUrl = `http://${username}:${password}@pr.oxylabs.io:7777`;
  console.log(`📡 Proxy URL: ${proxyUrl.replace(/:[^:@]*@/, ':****@')}`);

  const agent = new HttpsProxyAgent(proxyUrl);

  const options = {
    hostname: 'httpbin.org',
    port: 443,
    path: '/ip',
    method: 'GET',
    agent: agent,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      console.log(`📊 Status Code: ${res.statusCode}`);
      console.log(`📋 Headers:`, res.headers);

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('✅ Proxy test successful!');
          console.log(`🌐 Your IP through proxy: ${result.origin}`);
          resolve(result);
        } catch (e) {
          console.log('📄 Raw response:', data);
          resolve(data);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Proxy test failed:', error.message);
      reject(error);
    });

    req.setTimeout(30000, () => {
      console.error('❌ Proxy test timed out');
      req.destroy();
      reject(new Error('Timeout'));
    });

    req.end();
  });
}

// Test the proxy
try {
  await testProxy();
} catch (error) {
  console.error('❌ Test failed:', error.message);
}
