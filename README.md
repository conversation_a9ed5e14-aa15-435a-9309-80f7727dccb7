# Zillow Scraper with Anti-Blocking Features

This enhanced Zillow scraper implements comprehensive anti-blocking measures to reliably bypass Zillow's bot detection systems.

## Features

✅ **Stealth Mode**: Uses playwright-extra with stealth plugin to hide automation footprints  
✅ **Proxy Support**: Configurable proxy rotation for IP diversity  
✅ **Session Management**: Automatic session pooling with retry logic  
✅ **Header Randomization**: Rotates user agents and HTTP headers  
✅ **Human-like Behavior**: Random delays and mouse movements  
✅ **Blocking Detection**: Automatically detects and handles blocked requests  
✅ **Configurable**: Easy configuration through `scraper-config.js`

## Installation

Dependencies are already installed. If you need to reinstall:

```bash
npm install
```

## Configuration

Edit `scraper-config.js` to customize the scraper behavior:

### Proxy Setup (Recommended)

To use proxies, edit the config file:

```javascript
proxies: {
  enabled: true,  // Set to true to enable proxies
  urls: [
    'http://user:<EMAIL>:8000',
    'http://user:<EMAIL>:8000',
    // Add more proxy URLs
  ]
}
```

### Other Settings

- `sessions.maxPoolSize`: Number of concurrent sessions (default: 50)
- `crawler.maxConcurrency`: Concurrent requests (default: 3)
- `crawler.requestDelay`: Random delay between requests (1-3 seconds)
- `crawler.headless`: Run in headless mode (default: true)

## Usage

Run the scraper:

```bash
node scrape_zillow.js
```

## How It Works

### 1. Stealth Plugin
- Masks `navigator.webdriver` property
- Spoofs WebGL fingerprints
- Hides automation indicators
- Mimics real browser behavior

### 2. Proxy Rotation
- Rotates through proxy pool for each session
- Prevents IP-based blocking
- Supports residential and datacenter proxies

### 3. Session Pool
- Maintains cookie sessions across requests
- Automatically retries with new session on 403 errors
- Limits session reuse to avoid detection

### 4. Header Randomization
- Rotates realistic user agents
- Sets proper HTTP headers
- Uses random referers

### 5. Blocking Detection
- Monitors page content for blocking indicators
- Automatically retires blocked sessions
- Throws errors to trigger retry logic

## Troubleshooting

### Still Getting 403 Errors?

1. **Enable Proxies**: The most effective solution is using residential proxies
2. **Reduce Concurrency**: Lower `maxConcurrency` in config
3. **Increase Delays**: Adjust `requestDelay` for longer waits
4. **Check Logs**: Look for blocking detection messages

### Common Issues

- **No proxies**: Add quality residential proxies for best results
- **Too aggressive**: Reduce concurrency and increase delays
- **Session exhaustion**: Increase `maxPoolSize` or reduce `maxUsageCount`

## Best Practices

1. **Use Residential Proxies**: Most effective against IP blocking
2. **Start Conservative**: Begin with low concurrency and increase gradually
3. **Monitor Logs**: Watch for blocking patterns and adjust accordingly
4. **Respect Rate Limits**: Don't overwhelm the target site
5. **Test Configuration**: Verify settings work before large-scale scraping

## Legal Notice

This tool is for educational purposes. Ensure compliance with:
- Website terms of service
- Local laws and regulations
- Ethical scraping practices
- Rate limiting and respectful usage

## Support

If you encounter issues:
1. Check the configuration settings
2. Review the logs for error patterns
3. Adjust concurrency and delays
4. Consider using higher-quality proxies
