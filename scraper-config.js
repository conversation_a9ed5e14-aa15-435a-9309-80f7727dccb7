// scraper-config.js
// Configuration file for Zillow scraper anti-blocking settings

export const config = {
  // Proxy settings - Temporarily disabled due to connection issues
  proxies: {
    enabled: true, // Enable proxies to bypass IP blocks
    urls: [
      'http://MVPLEE:<EMAIL>:7777'
      // add more if you want country‑, ASN‑, or session‑pinned variants later
      // e.g. 'http://customer-MVPLEE-cc-US:<EMAIL>:7777'
    ]
  },

  // Session pool settings
  sessions: {
    maxPoolSize: 1, // Use only 1 session to keep same browser window
    maxErrorScore: 3, // Allow more retries before retiring session
    maxUsageCount: 100 // Allow more reuse of the same session
  },

  // Crawler settings
  crawler: {
    maxConcurrency: 1, // Very conservative - single request at a time
    headless: false, // Run in visible mode to appear more human
    requestDelay: {
      min: 15000, // minimum delay in ms - very conservative (15 seconds)
      max: 30000  // maximum delay in ms - very conservative (30 seconds)
    }
  },

  // Browser launch arguments for stealth
  browserArgs: [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--disable-blink-features=AutomationControlled',
    '--disable-features=VizDisplayCompositor',
    '--disable-ipc-flooding-protection',
    '--disable-renderer-backgrounding',
    '--disable-backgrounding-occluded-windows',
    '--disable-client-side-phishing-detection',
    '--disable-sync',
    '--disable-default-apps',
    '--disable-extensions',
    '--no-first-run',
    '--no-default-browser-check',
    '--disable-web-security',
    '--allow-running-insecure-content',
    '--disable-component-update',
    '--disable-background-timer-throttling',
    '--disable-renderer-backgrounding',
    '--disable-field-trial-config'
  ],

  // Realistic user agents
  userAgents: [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  ],

  // Common referers
  referers: [
    'https://www.google.com/',
    'https://www.zillow.com/',
    'https://www.realtor.com/',
    'https://www.redfin.com/',
    'https://www.bing.com/'
  ],

  // Blocking detection keywords
  blockingKeywords: [
    'Access Denied',
    'Blocked',
    'captcha',
    'Please verify you are a human',
    'Security check',
    'Bot detection',
    'Unusual traffic'
  ]
};
