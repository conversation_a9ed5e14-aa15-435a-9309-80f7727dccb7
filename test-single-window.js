// test-single-window.js
// Simple test to verify single browser window behavior

import { PlaywrightCrawler } from 'crawlee';
import { config } from './scraper-config.js';

const crawler = new PlaywrightCrawler({
  // Browser pool configuration to reuse same browser
  browserPoolOptions: {
    maxOpenPagesPerBrowser: 1,
    retireBrowserAfterPageCount: 100,
  },
  
  // Session pool for single persistent session
  sessionPoolOptions: {
    maxPoolSize: 1,
    sessionOptions: { 
      maxErrorScore: 5, // Allow more retries
      maxUsageCount: 100
    },
  },
  
  launchContext: { 
    launchOptions: { 
      headless: false, // Visible browser
      args: ['--disable-blink-features=AutomationControlled']
    } 
  },
  
  maxConcurrency: 1,
  
  requestHandler: async ({ page, log, session }) => {
    log.info(`Testing with session ${session.id}`);
    
    // Set viewport
    await page.setViewportSize({ width: 1366, height: 768 });
    
    // Wait and log
    await page.waitForTimeout(3000);
    log.info(`Page title: ${await page.title()}`);
    
    // Simple data extraction
    const url = page.url();
    log.info(`Successfully loaded: ${url}`);
    
    return { url, title: await page.title() };
  }
});

// Test with multiple URLs to see if same window is reused
await crawler.run([
  'https://www.zillow.com/profile/mccannteam',
  'https://www.zillow.com/profile/mccannteam' // Same URL twice to test retry
]);

console.log('Test completed - check if only one browser window was used');
