// scrape_zillow_proxy.js
// Simplified <PERSON><PERSON><PERSON> scraper with proper proxy handling

import playwright from 'playwright';
import * as cheerio from 'cheerio';
import { config } from './scraper-config.js';

function getRandomUserAgent() {
  return config.userAgents[Math.floor(Math.random() * config.userAgents.length)];
}

function getRandomReferer() {
  return config.referers[Math.floor(Math.random() * config.referers.length)];
}

async function solveHumanChallenge(page, log = console) {
  for (let attempt = 0; attempt < 3; attempt++) {
    await page.waitForTimeout(1000 + Math.random() * 2000);
    
    const btn = await page.$('text=Press and Hold');
    if (!btn) {
      if (attempt === 0) {
        log.info('No human challenge detected on this attempt');
      }
      return true;
    }
    
    const box = await btn.boundingBox();
    if (!box) {
      log.warn('Challenge button found but no bounding box');
      continue;
    }
    
    const cx = box.x + box.width/2;
    const cy = box.y + box.height/2;

    log.info(`HUMAN challenge detected – solving… (attempt ${attempt + 1})`);
    
    await page.mouse.move(cx, cy, { steps: 10 });
    await page.waitForTimeout(500);
    await page.mouse.down();
    log.info('Holding button...');

    const holdTime = 6000 + Math.random() * 3000;
    await page.waitForTimeout(holdTime);

    await page.mouse.up();
    log.info('Released button, waiting for verification...');
    
    await page.waitForTimeout(3000);
    
    const stillPresent = await page.$('text=Press and Hold');
    if (!stillPresent) {
      log.info('✅ Human challenge solved successfully!');
      return true;
    }
    
    log.warn(`❌ Challenge still present after attempt ${attempt + 1}, retrying...`);
    await page.waitForTimeout(2000);
  }
  
  log.error('Failed to solve human challenge after 3 attempts');
  return false;
}

function extractDataWithCheerio(html, pageUrl) {
  const $ = cheerio.load(html);
  
  // Helper function to safely extract text
  const getText = (selector) => {
    const element = $(selector);
    return element.length > 0 ? element.text().trim() : '';
  };
  
  // Helper function to get attribute
  const getAttr = (selector, attr) => {
    const element = $(selector);
    return element.length > 0 ? element.attr(attr) || '' : '';
  };
  
  // Helper function to extract phone number
  const extractPhone = (text) => {
    const phoneMatch = text.match(/\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/);
    return phoneMatch ? phoneMatch[0].replace(/\D/g, '') : '';
  };

  // Extract basic profile information
  const name = getText('h1') || getText('[data-testid="agent-name"]');
  
  // Extract contact information
  const phoneText = getText('a[href^="tel:"]') || getText('[data-testid="agent-phone"]');
  const phone = extractPhone(phoneText);
  
  // Extract business information
  const businessName = getText('[data-testid="agent-company"]') || 
                      getText('.brokerage-name') ||
                      getText('.company-name');
  
  // Extract profile photo
  const profilePhoto = getAttr('img[alt*="Profile"]', 'src') ||
                      getAttr('[data-testid="agent-photo"] img', 'src');
  
  // Extract ratings and reviews
  const ratingText = getText('[data-testid="agent-rating"]') || getText('.rating');
  const reviewCountText = getText('[data-testid="review-count"]') || getText('.review-count');
  
  // Extract sales statistics from visible text
  const salesStatsText = $('body').text();
  const salesCountMatch = salesStatsText.match(/(\d+)\s+sales?/i);
  const priceRangeMatch = salesStatsText.match(/\$([0-9,]+).*?\$([0-9,]+)/);
  
  // Extract email
  const email = getAttr('a[href^="mailto:"]', 'href')?.replace('mailto:', '') || null;

  return {
    // Basic profile information
    url: pageUrl,
    encodedZuid: null,
    screenName: name,
    inCanada: false,
    name: name,
    flag: null,
    profileTypeIds: [],
    profileTypes: [],
    sidebarVideoUrl: getAttr('video', 'src'),

    // Business information
    businessAddress: {
      address1: null,
      address2: null,
      city: null,
      state: null,
      postalCode: null
    },
    businessName: businessName,
    cpdUserPronouns: null,

    // Agent status
    isTopAgent: $('body').text().toLowerCase().includes('top agent'),
    isPremierAgent: $('body').text().toLowerCase().includes('premier agent'),

    // Profile media
    profileImageId: null,
    profilePhotoSrc: profilePhoto,

    // Ratings and reviews
    ratings: {
      totalCount: Number.parseInt(reviewCountText.match(/\d+/)?.[0] || '0'),
      averageRating: Number.parseFloat(ratingText.match(/[\d.]+/)?.[0] || '0')
    },

    // Contact information
    phoneNumbers: {
      cell: null,
      brokerage: null,
      business: phone
    },
    email: email,

    // Professional information
    professional: {},
    getToKnowMe: {
      description: getText('[data-testid="agent-bio"], .agent-bio, .description')
    },
    agentLicenses: [],

    // Sales statistics
    agentSalesStats: {
      countAllTime: Number.parseInt(salesCountMatch?.[1] || '0'),
      countLastYear: 0,
      priceRangeThreeYearMin: priceRangeMatch ? Number.parseInt(priceRangeMatch[1].replace(/,/g, '')) : null,
      priceRangeThreeYearMax: priceRangeMatch ? Number.parseInt(priceRangeMatch[2].replace(/,/g, '')) : null,
      averageValueThreeYear: null,
      stats_include_team: $('body').text().toLowerCase().includes('team')
    },

    // Listings data
    forSaleListings: [],
    forRentListings: [],

    // Past sales
    pastSales: {
      totalSales: Number.parseInt(salesCountMatch?.[1] || '0'),
      past_sales: []
    },

    // Additional data
    preferredLenders: {},
    professionalInformation: [],

    // Reviews data
    reviewsData: {
      reviews: [],
      subRatings: {},
      reviewee: {
        name: name
      }
    },

    // Team information
    teamDisplayInformation: {
      teamLeadInfo: {},
      teamMemberInfo: []
    },

    // Metadata
    scrapedAt: new Date().toISOString(),
    pageTitle: $('title').text(),
    
    // Debug info
    _debug: {
      extractionMethod: 'cheerio',
      proxyUsed: config.proxies.enabled,
      elementsFound: {
        propertyCards: $('[data-testid="property-card"], .property-card').length,
        reviews: $('[data-testid="review"], .review').length,
        teamMembers: $('[data-testid="team-member"], .team-member').length
      }
    }
  };
}

async function scrapeWithProxy(url) {
  console.log('🔗 Attempting to scrape with proxy...');
  
  let browser;
  try {
    browser = await playwright.chromium.launch({
      headless: false,
      args: config.browserArgs
    });

    const proxyUrl = config.proxies.urls[0];
    const proxyMatch = proxyUrl.match(/http:\/\/([^:]+):([^@]+)@([^:]+):(\d+)/);
    
    if (!proxyMatch) {
      throw new Error('Invalid proxy URL format');
    }

    const contextOptions = {
      userAgent: getRandomUserAgent(),
      viewport: { 
        width: 1366 + Math.floor(Math.random() * 200), 
        height: 768 + Math.floor(Math.random() * 200) 
      },
      locale: 'en-US',
      timezoneId: 'America/New_York',
      permissions: ['geolocation'],
      geolocation: { latitude: 40.7128, longitude: -74.0060 },
      colorScheme: 'light',
      proxy: {
        server: `http://${proxyMatch[3]}:${proxyMatch[4]}`,
        username: proxyMatch[1],
        password: decodeURIComponent(proxyMatch[2])
      }
    };

    console.log(`🔗 Using proxy: ${proxyMatch[3]}:${proxyMatch[4]} with username: ${proxyMatch[1]}`);

    const context = await browser.newContext(contextOptions);
    const page = await context.newPage();

    // Apply stealth techniques
    await page.addInitScript(() => {
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      window.chrome = {
        runtime: {},
        loadTimes: () => {},
        csi: () => {},
        app: {}
      };
    });

    // Set headers
    await page.setExtraHTTPHeaders({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0',
      'Referer': getRandomReferer()
    });

    // Test proxy connection
    console.log('🔄 Testing proxy connection...');
    await page.goto('https://www.zillow.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log('✅ Proxy connection successful!');
    
    // Navigate to target URL
    console.log(`🎯 Navigating to target URL: ${url}...`);
    const response = await page.goto(url, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log(`📊 Response status: ${response.status()}`);
    
    await page.waitForTimeout(3000);
    
    const challengeSolved = await solveHumanChallenge(page);
    if (!challengeSolved) {
      console.error('❌ Failed to solve human challenge');
      return null;
    }
    
    // Get the HTML content for Cheerio
    console.log('📄 Extracting HTML content for Cheerio parsing...');
    const html = await page.content();
    
    // Extract data using Cheerio
    console.log('🔍 Parsing data with Cheerio...');
    const data = extractDataWithCheerio(html, url);
    
    console.log('✅ Data extracted successfully!');
    
    // Save to file
    const fs = await import('node:fs');
    const filename = `zillow_profile_proxy_${Date.now()}.json`;
    await fs.promises.writeFile(filename, JSON.stringify(data, null, 2));
    console.log(`💾 Data saved to ${filename}`);
    
    await browser.close();
    return data;
    
  } catch (error) {
    console.error('❌ Proxy scraping failed:', error.message);
    if (browser) {
      await browser.close();
    }
    return null;
  }
}

// Run the scraper
await scrapeWithProxy('https://www.zillow.com/profile/mccannteam');

export { scrapeWithProxy, extractDataWithCheerio, solveHumanChallenge };
