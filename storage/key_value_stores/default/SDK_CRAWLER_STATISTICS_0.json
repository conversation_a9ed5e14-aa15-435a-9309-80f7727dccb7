{"requestsFinished": 0, "requestsFailed": 1, "requestsRetries": 1, "requestsFailedPerMinute": 7, "requestsFinishedPerMinute": 0, "requestMinDurationMillis": null, "requestMaxDurationMillis": 0, "requestTotalFailedDurationMillis": 1899, "requestTotalFinishedDurationMillis": 0, "crawlerStartedAt": "2025-05-26T00:06:52.732Z", "crawlerFinishedAt": "2025-05-26T00:07:01.164Z", "statsPersistedAt": "2025-05-26T00:07:01.164Z", "crawlerRuntimeMillis": 8557, "crawlerLastStartTimestamp": 1748218012607, "requestRetryHistogram": [null, null, null, 1], "statsId": 0, "requestAvgFailedDurationMillis": 1899, "requestAvgFinishedDurationMillis": null, "requestTotalDurationMillis": 1899, "requestsTotal": 1, "requestsWithStatusCode": {"403": 4}, "errors": {"/Users/<USER>/Project_Folder/MyScraper/node_modules/@crawlee/basic/internals/basic-crawler.js:778:19": {"missing error code": {"Error": {"Request blocked - received 403 status code.": {"count": 1}}}}}, "retryErrors": {"/Users/<USER>/Project_Folder/MyScraper/node_modules/@crawlee/basic/internals/basic-crawler.js:778:19": {"missing error code": {"Error": {"Request blocked - received 403 status code.": {"count": 3}}}}}}