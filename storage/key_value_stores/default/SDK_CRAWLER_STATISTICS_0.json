{"requestsFinished": 0, "requestsFailed": 1, "requestsRetries": 1, "requestsFailedPerMinute": 6, "requestsFinishedPerMinute": 0, "requestMinDurationMillis": null, "requestMaxDurationMillis": 0, "requestTotalFailedDurationMillis": 2036, "requestTotalFinishedDurationMillis": 0, "crawlerStartedAt": "2025-05-26T03:07:05.760Z", "crawlerFinishedAt": "2025-05-26T03:07:14.843Z", "statsPersistedAt": "2025-05-26T03:07:14.843Z", "crawlerRuntimeMillis": 9212, "crawlerLastStartTimestamp": 1748228825631, "requestRetryHistogram": [null, null, null, 1], "statsId": 0, "requestAvgFailedDurationMillis": 2036, "requestAvgFinishedDurationMillis": null, "requestTotalDurationMillis": 2036, "requestsTotal": 1, "requestsWithStatusCode": {"403": 4}, "errors": {"/Users/<USER>/Project_Folder/MyScraper/node_modules/@crawlee/basic/internals/basic-crawler.js:778:19": {"missing error code": {"Error": {"Request blocked - received 403 status code.": {"count": 1}}}}}, "retryErrors": {"/Users/<USER>/Project_Folder/MyScraper/node_modules/@crawlee/basic/internals/basic-crawler.js:778:19": {"missing error code": {"Error": {"Request blocked - received 403 status code.": {"count": 3}}}}}}