{"requestsFinished": 0, "requestsFailed": 1, "requestsRetries": 1, "requestsFailedPerMinute": 4, "requestsFinishedPerMinute": 0, "requestMinDurationMillis": null, "requestMaxDurationMillis": 0, "requestTotalFailedDurationMillis": 2148, "requestTotalFinishedDurationMillis": 0, "crawlerStartedAt": "2025-05-26T00:05:27.311Z", "crawlerFinishedAt": "2025-05-26T00:05:40.001Z", "statsPersistedAt": "2025-05-26T00:05:40.001Z", "crawlerRuntimeMillis": 12817, "crawlerLastStartTimestamp": 1748217927184, "requestRetryHistogram": [null, null, null, 1], "statsId": 0, "requestAvgFailedDurationMillis": 2148, "requestAvgFinishedDurationMillis": null, "requestTotalDurationMillis": 2148, "requestsTotal": 1, "requestsWithStatusCode": {"403": 4}, "errors": {"/Users/<USER>/Project_Folder/MyScraper/node_modules/@crawlee/basic/internals/basic-crawler.js:778:19": {"missing error code": {"Error": {"Request blocked - received 403 status code.": {"count": 1}}}}}, "retryErrors": {"/Users/<USER>/Project_Folder/MyScraper/node_modules/@crawlee/basic/internals/basic-crawler.js:778:19": {"missing error code": {"Error": {"Request blocked - received 403 status code.": {"count": 3}}}}}}