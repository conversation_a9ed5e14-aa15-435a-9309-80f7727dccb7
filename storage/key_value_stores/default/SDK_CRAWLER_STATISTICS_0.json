{"requestsFinished": 0, "requestsFailed": 1, "requestsRetries": 1, "requestsFailedPerMinute": 1, "requestsFinishedPerMinute": 0, "requestMinDurationMillis": null, "requestMaxDurationMillis": 0, "requestTotalFailedDurationMillis": 1270, "requestTotalFinishedDurationMillis": 0, "crawlerStartedAt": "2025-05-25T23:55:31.654Z", "crawlerFinishedAt": "2025-05-25T23:56:07.713Z", "statsPersistedAt": "2025-05-25T23:56:07.713Z", "crawlerRuntimeMillis": 36191, "crawlerLastStartTimestamp": 1748217331522, "requestRetryHistogram": [null, null, null, 1], "statsId": 0, "requestAvgFailedDurationMillis": 1270, "requestAvgFinishedDurationMillis": null, "requestTotalDurationMillis": 1270, "requestsTotal": 1, "requestsWithStatusCode": {"200": 1, "403": 3}, "errors": {"/Users/<USER>/Project_Folder/MyScraper/node_modules/@crawlee/basic/internals/basic-crawler.js:778:19": {"missing error code": {"Error": {"Request blocked - received 403 status code.": {"count": 1}}}}}, "retryErrors": {"/Users/<USER>/Project_Folder/MyScraper/scrape_zillow.js:38:18": {"missing error code": {"TimeoutError": {"page.waitForLoadState: Timeout 30000ms exceeded.": {"count": 1}}}}, "/Users/<USER>/Project_Folder/MyScraper/node_modules/@crawlee/basic/internals/basic-crawler.js:778:19": {"missing error code": {"Error": {"Request blocked - received 403 status code.": {"count": 2}}}}}}