// scrape_zillow_cheerio.js
// Enhanced <PERSON><PERSON><PERSON> scraper using <PERSON><PERSON> + <PERSON>eer<PERSON> for better data extraction

import playwright from 'playwright';
import * as cheerio from 'cheerio';
import { config } from './scraper-config.js';

function getRandomUserAgent() {
  return config.userAgents[Math.floor(Math.random() * config.userAgents.length)];
}

function getRandomReferer() {
  return config.referers[Math.floor(Math.random() * config.referers.length)];
}

async function solveHumanChallenge(page, log = console) {
  for (let attempt = 0; attempt < 5; attempt++) {
    await page.waitForTimeout(1000 + Math.random() * 2000);

    const btn = await page.$('text=Press and Hold');
    if (!btn) {
      if (attempt === 0) {
        log.info('No human challenge detected on this attempt');
      }
      return true;
    }

    const box = await btn.boundingBox();
    if (!box) {
      log.warn('Challenge button found but no bounding box');
      continue;
    }

    const cx = box.x + box.width/2;
    const cy = box.y + box.height/2;

    log.info(`HUMAN challenge detected – solving… (attempt ${attempt + 1})`);

    await page.mouse.move(
      cx + (Math.random() - 0.5) * 20,
      cy + (Math.random() - 0.5) * 20,
      { steps: 15 + Math.floor(Math.random() * 10) }
    );

    await page.waitForTimeout(500 + Math.random() * 1000);
    await page.mouse.move(cx, cy, { steps: 5 });
    await page.waitForTimeout(200 + Math.random() * 300);

    await page.mouse.down();
    log.info('Holding button...');

    const holdTime = 6000 + Math.random() * 3000;
    const start = Date.now();
    let moveCount = 0;

    while (Date.now() - start < holdTime) {
      if (moveCount % 10 === 0) {
        await page.mouse.move(
          cx + (Math.random() - 0.5) * 2,
          cy + (Math.random() - 0.5) * 2,
          { steps: 1 }
        );
      }
      moveCount++;
      await page.waitForTimeout(150 + Math.random() * 100);
    }

    await page.mouse.up();
    log.info('Released button, waiting for verification...');

    await page.waitForTimeout(3000 + Math.random() * 2000);

    try {
      await Promise.race([
        page.waitForSelector('text=Press and Hold', { state: 'detached', timeout: 5000 }),
        page.waitForLoadState('networkidle', { timeout: 5000 }),
        page.waitForTimeout(5000)
      ]);
    } catch (e) {
      log.info('Timeout waiting for challenge resolution');
    }

    const stillPresent = await page.$('text=Press and Hold');
    if (!stillPresent) {
      log.info('✅ Human challenge solved successfully!');
      return true;
    }

    log.warn(`❌ Challenge still present after attempt ${attempt + 1}, retrying...`);
    await page.waitForTimeout(3000 + Math.random() * 2000);

    if (attempt >= 2) {
      log.info('Refreshing page to get new challenge...');
      await page.reload({ waitUntil: 'networkidle' });
      await page.waitForTimeout(3000);
    }
  }

  log.error('Failed to solve human challenge after 5 attempts');
  return false;
}

function extractDataWithCheerio(html, pageUrl) {
  const $ = cheerio.load(html);

  // Helper function to safely extract text
  const getText = (selector) => {
    const element = $(selector);
    return element.length > 0 ? element.text().trim() : '';
  };

  // Helper function to get attribute
  const getAttr = (selector, attr) => {
    const element = $(selector);
    return element.length > 0 ? element.attr(attr) || '' : '';
  };

  // Helper function to extract phone number
  const extractPhone = (text) => {
    const phoneMatch = text.match(/\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/);
    return phoneMatch ? phoneMatch[0].replace(/\D/g, '') : '';
  };

  // Extract basic profile information
  const name = getText('h1') || getText('[data-testid="agent-name"]');

  // Extract contact information
  const phoneText = getText('a[href^="tel:"]') || getText('[data-testid="agent-phone"]');
  const phone = extractPhone(phoneText);

  // Extract business information
  const businessName = getText('[data-testid="agent-company"]') ||
                      getText('.brokerage-name') ||
                      getText('.company-name');

  // Extract profile photo
  const profilePhoto = getAttr('img[alt*="Profile"]', 'src') ||
                      getAttr('[data-testid="agent-photo"] img', 'src');

  // Extract ratings and reviews
  const ratingText = getText('[data-testid="agent-rating"]') || getText('.rating');
  const reviewCountText = getText('[data-testid="review-count"]') || getText('.review-count');

  // Extract listings information
  const forSaleListings = [];
  const forRentListings = [];

  // Look for property cards with more specific Zillow selectors
  $('article[data-testid*="property"], [data-testid="property-card"], .property-card, .listing-card, a[href*="/homedetails/"]').each((i, element) => {
    const $card = $(element).closest('article, div, li');
    if ($card.length === 0) return;

    const cardText = $card.text();
    const cardHtml = $card.html();

    // Extract ZPID from any href in the card
    const zpidMatch = cardHtml.match(/\/(\d+)_zpid/) || cardHtml.match(/zpid[=:](\d+)/);
    const zpid = zpidMatch ? zpidMatch[1] : null;

    // Extract address - try multiple selectors
    const address = getText($card.find('[data-testid*="address"], .address, .property-address')) ||
                   cardText.match(/\d+\s+[A-Za-z\s]+(?:St|Ave|Rd|Dr|Ln|Blvd|Way|Ct|Pl)/)?.[0] || '';

    // Extract price - look for dollar amounts
    const priceMatch = cardText.match(/\$[\d,]+/) || cardText.match(/\$\d+[KM]?/);
    const price = priceMatch ? priceMatch[0] : '';

    // Extract bed/bath info
    const bedMatch = cardText.match(/(\d+)\s*(?:bed|bd|bedroom)/i);
    const bathMatch = cardText.match(/(\d+(?:\.\d+)?)\s*(?:bath|ba|bathroom)/i);

    const listing = {
      zpid: zpid,
      home_type: getText($card.find('[data-testid*="type"], .property-type')) ||
                (cardText.includes('Condo') ? 'Condo' :
                 cardText.includes('Townhouse') ? 'Townhouse' :
                 cardText.includes('Single Family') ? 'Single Family' : null),
      address: address,
      bedrooms: bedMatch ? parseInt(bedMatch[1]) : null,
      bathrooms: bathMatch ? parseFloat(bathMatch[1]) : null,
      price: price,
      latitude: null,
      longitude: null,
      brokerage_name: businessName,
      home_marketing_status: getText($card.find('[data-testid*="status"], .status')) ||
                            (cardText.includes('For Sale') ? 'For Sale' :
                             cardText.includes('For Rent') ? 'For Rent' :
                             cardText.includes('Sold') ? 'Sold' : null),
      listing_url: getAttr($card.find('a[href*="/homedetails/"], a[href*="/b/"]'), 'href') || ''
    };

    // Only add if we have meaningful data
    if (listing.zpid || listing.address || listing.price) {
      // Determine if it's for sale or rent based on context
      const cardTextLower = cardText.toLowerCase();
      if (cardTextLower.includes('for rent') || cardTextLower.includes('rental') || cardTextLower.includes('/rent/')) {
        forRentListings.push(listing);
      } else {
        forSaleListings.push(listing);
      }
    }
  });

  // Extract sales statistics from visible text
  const salesStatsText = $('body').text();
  const salesCountMatch = salesStatsText.match(/(\d+)\s+sales?/i);
  const priceRangeMatch = salesStatsText.match(/\$([0-9,]+).*?\$([0-9,]+)/);

  // Extract reviews
  const reviews = [];
  $('[data-testid="review"], .review').each((i, element) => {
    const $review = $(element);
    reviews.push({
      reviewComment: getText($review.find('[data-testid="review-comment"], .review-text')),
      reviewId: getAttr($review, 'data-review-id') || `review_${i}`,
      rating: getText($review.find('[data-testid="review-rating"], .rating')).match(/\d+/)?.[0] || null,
      createDate: getText($review.find('[data-testid="review-date"], .review-date'))
    });
  });

  // Extract team information
  const teamMembers = [];
  $('[data-testid="team-member"], .team-member').each((i, element) => {
    const $member = $(element);
    teamMembers.push({
      name: getText($member.find('[data-testid="member-name"], .member-name')),
      role: getText($member.find('[data-testid="member-role"], .member-role')),
      photo: getAttr($member.find('img'), 'src')
    });
  });

  return {
    // Basic profile information
    url: pageUrl,
    encodedZuid: null, // Would need to extract from script tags
    screenName: name,
    inCanada: false, // Would need to determine from address
    name: name,
    flag: null,
    profileTypeIds: [],
    profileTypes: [],
    sidebarVideoUrl: getAttr('video', 'src'),

    // Business information
    businessAddress: {
      address1: null, // Would need more specific selectors
      address2: null,
      city: null,
      state: null,
      postalCode: null
    },
    businessName: businessName,
    cpdUserPronouns: null,

    // Agent status
    isTopAgent: $('body').text().toLowerCase().includes('top agent'),
    isPremierAgent: $('body').text().toLowerCase().includes('premier agent'),

    // Profile media
    profileImageId: null,
    profilePhotoSrc: profilePhoto,

    // Ratings and reviews
    ratings: {
      totalCount: parseInt(reviewCountText.match(/\d+/)?.[0] || '0'),
      averageRating: parseFloat(ratingText.match(/[\d.]+/)?.[0] || '0')
    },

    // Contact information
    phoneNumbers: {
      cell: null,
      brokerage: null,
      business: phone
    },
    email: getAttr('a[href^="mailto:"]', 'href')?.replace('mailto:', '') || null,

    // Professional information
    professional: {},
    getToKnowMe: {
      description: getText('[data-testid="agent-bio"], .agent-bio, .description')
    },
    agentLicenses: [], // Would need specific extraction logic

    // Sales statistics
    agentSalesStats: {
      countAllTime: parseInt(salesCountMatch?.[1] || '0'),
      countLastYear: 0, // Would need more specific extraction
      priceRangeThreeYearMin: priceRangeMatch ? parseInt(priceRangeMatch[1].replace(/,/g, '')) : null,
      priceRangeThreeYearMax: priceRangeMatch ? parseInt(priceRangeMatch[2].replace(/,/g, '')) : null,
      averageValueThreeYear: null,
      stats_include_team: $('body').text().toLowerCase().includes('team')
    },

    // Listings data
    forSaleListings: forSaleListings,
    forRentListings: forRentListings,

    // Past sales
    pastSales: {
      totalSales: parseInt(salesCountMatch?.[1] || '0'),
      past_sales: [] // Would need specific extraction from sales history
    },

    // Additional data
    preferredLenders: {},
    professionalInformation: [],

    // Reviews data
    reviewsData: {
      reviews: reviews,
      subRatings: {},
      reviewee: {
        name: name
      }
    },

    // Team information
    teamDisplayInformation: {
      teamLeadInfo: teamMembers.length > 0 ? teamMembers[0] : {},
      teamMemberInfo: teamMembers
    },

    // Metadata
    scrapedAt: new Date().toISOString(),
    pageTitle: $('title').text(),

    // Debug info
    _debug: {
      extractionMethod: 'cheerio',
      elementsFound: {
        propertyCards: $('[data-testid="property-card"], .property-card').length,
        reviews: $('[data-testid="review"], .review').length,
        teamMembers: $('[data-testid="team-member"], .team-member').length
      }
    }
  };
}

async function scrapeZillowWithCheerio(url) {
  const browser = await playwright.chromium.launch({
    headless: false,
    args: config.browserArgs
  });

  const contextOptions = {
    userAgent: getRandomUserAgent(),
    viewport: {
      width: 1366 + Math.floor(Math.random() * 200),
      height: 768 + Math.floor(Math.random() * 200)
    },
    locale: 'en-US',
    timezoneId: 'America/New_York',
    permissions: ['geolocation'],
    geolocation: { latitude: 40.7128, longitude: -74.0060 },
    colorScheme: 'light'
  };

  // Add proxy if enabled
  if (config.proxies.enabled && config.proxies.urls.length > 0) {
    const proxyUrl = config.proxies.urls[0]; // Use first proxy
    const proxyMatch = proxyUrl.match(/http:\/\/([^:]+):([^@]+)@([^:]+):(\d+)/);
    if (proxyMatch) {
      contextOptions.proxy = {
        server: `http://${proxyMatch[3]}:${proxyMatch[4]}`,
        username: proxyMatch[1],
        password: decodeURIComponent(proxyMatch[2])
      };
      console.log(`Using proxy: ${proxyMatch[3]}:${proxyMatch[4]}`);
    }
  }

  const context = await browser.newContext(contextOptions);

  const page = await context.newPage();

  try {
    // Apply stealth techniques
    await page.addInitScript(() => {
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      window.chrome = {
        runtime: {},
        loadTimes: () => {},
        csi: () => {},
        app: {}
      };

      Object.defineProperty(navigator, 'plugins', {
        get: () => ({
          length: 3,
          0: { name: 'Chrome PDF Plugin' },
          1: { name: 'Chrome PDF Viewer' },
          2: { name: 'Native Client' }
        }),
      });
    });

    // Set headers
    await page.setExtraHTTPHeaders({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0',
      'Referer': getRandomReferer()
    });

    // Warm up session
    console.log('Warming up session by visiting Zillow homepage...');
    await page.goto('https://www.zillow.com', {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });

    await page.waitForTimeout(2000 + Math.random() * 3000);
    await page.mouse.move(Math.random() * 500, Math.random() * 500, { steps: 10 });
    await page.waitForTimeout(1000);

    await page.evaluate(() => {
      window.scrollTo(0, Math.random() * 300);
    });
    await page.waitForTimeout(2000);

    console.log(`Now navigating to target URL: ${url}...`);

    const response = await page.goto(url, {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });

    console.log(`Response status: ${response.status()}`);

    await page.waitForTimeout(3000);

    const challengeSolved = await solveHumanChallenge(page);
    if (!challengeSolved) {
      console.error('Failed to solve human challenge');
      return null;
    }

    try {
      await page.waitForLoadState('networkidle', { timeout: 30000 });
    } catch (e) {
      console.log('Page did not reach networkidle state, continuing...');
    }

    // Get the HTML content for Cheerio
    console.log('Extracting HTML content for Cheerio parsing...');
    const html = await page.content();

    // Extract data using Cheerio
    console.log('Parsing data with Cheerio...');
    const data = extractDataWithCheerio(html, url);

    console.log('Data extracted:', JSON.stringify(data, null, 2));

    // Save to file
    const fs = await import('node:fs');
    const filename = `zillow_profile_cheerio_${Date.now()}.json`;
    await fs.promises.writeFile(filename, JSON.stringify(data, null, 2));
    console.log(`Data saved to ${filename}`);

    return data;

  } catch (error) {
    console.error('Error during scraping:', error);
    return null;
  } finally {
    console.log('Keeping browser open for 10 seconds...');
    await page.waitForTimeout(10000);
    await browser.close();
  }
}

// Run the scraper with a different agent profile to test
await scrapeZillowWithCheerio('https://www.zillow.com/profile/Realtor-Keller-Williams/');

export { extractDataWithCheerio, solveHumanChallenge, scrapeZillowWithCheerio };
