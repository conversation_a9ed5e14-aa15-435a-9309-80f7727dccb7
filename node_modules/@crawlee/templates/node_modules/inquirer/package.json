{"name": "inquirer", "type": "module", "version": "9.3.7", "description": "A collection of common interactive command line user interfaces.", "author": "<PERSON> <<EMAIL>>", "files": ["lib"], "main": "lib/index.js", "keywords": ["answer", "answers", "ask", "base", "cli", "command", "command-line", "confirm", "enquirer", "generate", "generator", "hyper", "input", "inquire", "inquirer", "interface", "iterm", "javascript", "menu", "node", "nodejs", "prompt", "promptly", "prompts", "question", "readline", "scaffold", "scaffolder", "scaffolding", "stdin", "stdout", "terminal", "tty", "ui", "yeoman", "yo", "zsh"], "engines": {"node": ">=18"}, "devDependencies": {"terminal-link": "^3.0.0"}, "repository": {"type": "git", "url": "https://github.com/SBoudrias/Inquirer.js.git"}, "license": "MIT", "dependencies": {"@inquirer/figures": "^1.0.3", "ansi-escapes": "^4.3.2", "cli-width": "^4.1.0", "external-editor": "^3.1.0", "mute-stream": "1.0.0", "ora": "^5.4.1", "run-async": "^3.0.0", "rxjs": "^7.8.1", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wrap-ansi": "^6.2.0", "yoctocolors-cjs": "^2.1.2"}, "homepage": "https://github.com/SBoudrias/Inquirer.js/blob/master/packages/inquirer/README.md", "sideEffects": false, "gitHead": "06f69834ec9192f3269501b3580789715fee6bde"}