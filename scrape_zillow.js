// scrape_zillow.js
import { PlaywrightCrawler, ProxyConfiguration } from 'crawlee';
import { addExtra } from 'playwright-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import playwright from 'playwright';
import { config } from './scraper-config.js';

// Initialize playwright-extra with stealth plugin
const pw = addExtra(playwright);
pw.use(StealthPlugin());

function getRandomUserAgent() {
  return config.userAgents[Math.floor(Math.random() * config.userAgents.length)];
}

function getRandomReferer() {
  return config.referers[Math.floor(Math.random() * config.referers.length)];
}

function getRandomDelay() {
  return config.crawler.requestDelay.min +
         Math.random() * (config.crawler.requestDelay.max - config.crawler.requestDelay.min);
}

async function solveHumanChallenge(page, log) {
  // Try multiple times to find and solve the challenge
  for (let attempt = 0; attempt < 5; attempt++) {
    // Wait a bit before checking for challenge
    await page.waitForTimeout(1000 + Math.random() * 2000);

    const btn = await page.$('text=Press and Hold');
    if (!btn) {
      if (attempt === 0) {
        log.info('No human challenge detected on this attempt');
      }
      return true; // No challenge found, success
    }

    const box = await btn.boundingBox();
    if (!box) {
      log.warning('Challenge button found but no bounding box');
      continue;
    }

    const cx = box.x + box.width/2;
    const cy = box.y + box.height/2;

    log.info(`HUMAN challenge detected – solving… (attempt ${attempt + 1})`);

    // More human-like approach to the button
    await page.mouse.move(
      cx + (Math.random() - 0.5) * 20,
      cy + (Math.random() - 0.5) * 20,
      { steps: 15 + Math.floor(Math.random() * 10) }
    );

    // Pause before clicking like a human would
    await page.waitForTimeout(500 + Math.random() * 1000);

    // Move to exact center more naturally
    await page.mouse.move(cx, cy, { steps: 5 });
    await page.waitForTimeout(200 + Math.random() * 300);

    await page.mouse.down();
    log.info('Holding button...');

    // Much longer hold time with more variation
    const holdTime = 6000 + Math.random() * 3000; // 6-9 seconds
    const start = Date.now();
    let moveCount = 0;

    while (Date.now() - start < holdTime) {
      // More realistic micro-movements
      if (moveCount % 10 === 0) { // Move less frequently
        await page.mouse.move(
          cx + (Math.random() - 0.5) * 2, // Smaller movements
          cy + (Math.random() - 0.5) * 2,
          { steps: 1 }
        );
      }
      moveCount++;
      await page.waitForTimeout(150 + Math.random() * 100);
    }

    await page.mouse.up();
    log.info('Released button, waiting for verification...');

    // Wait longer for the challenge to process
    await page.waitForTimeout(3000 + Math.random() * 2000);

    // Check if challenge was solved by looking for page changes
    try {
      // Wait for either the challenge to disappear or page to change
      await Promise.race([
        page.waitForSelector('text=Press and Hold', { state: 'detached', timeout: 5000 }),
        page.waitForLoadState('networkidle', { timeout: 5000 }),
        page.waitForTimeout(5000)
      ]);
    } catch (e) {
      log.info('Timeout waiting for challenge resolution');
    }

    // Check if challenge is still present
    const stillPresent = await page.$('text=Press and Hold');
    if (!stillPresent) {
      log.info('✅ Human challenge solved successfully!');
      return true;
    }

    log.warning(`❌ Challenge still present after attempt ${attempt + 1}, retrying...`);

    // Wait longer between attempts
    await page.waitForTimeout(3000 + Math.random() * 2000);

    // Try refreshing the page on later attempts
    if (attempt >= 2) {
      log.info('Refreshing page to get new challenge...');
      await page.reload({ waitUntil: 'networkidle' });
      await page.waitForTimeout(3000);
    }
  }

  log.error('Failed to solve human challenge after 5 attempts');
  return false;
}

(async () => {
  const crawler = new PlaywrightCrawler({
    // Browser pool configuration to reuse same browser
    browserPoolOptions: {
      maxOpenPagesPerBrowser: 1,
      retireBrowserAfterPageCount: 100, // Keep browser alive longer
    },

    // Use stealth browser with pre-launch hook
    preNavigationHooks: [
      async ({ page }) => {
        // Apply comprehensive stealth techniques
        await page.addInitScript(() => {
          // Remove webdriver property
          Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
          });

          // Mock chrome property
          window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
          };

          // Mock plugins with realistic data
          Object.defineProperty(navigator, 'plugins', {
            get: () => ({
              length: 3,
              0: { name: 'Chrome PDF Plugin' },
              1: { name: 'Chrome PDF Viewer' },
              2: { name: 'Native Client' }
            }),
          });

          // Mock languages
          Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
          });

          // Mock permissions
          const originalQuery = window.navigator.permissions.query;
          window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
              Promise.resolve({ state: Notification.permission }) :
              originalQuery(parameters)
          );

          // Hide automation indicators
          Object.defineProperty(navigator, 'platform', {
            get: () => 'Win32',
          });

          // Mock screen properties
          Object.defineProperty(screen, 'width', { get: () => 1920 });
          Object.defineProperty(screen, 'height', { get: () => 1080 });
          Object.defineProperty(screen, 'availWidth', { get: () => 1920 });
          Object.defineProperty(screen, 'availHeight', { get: () => 1040 });
        });
      }
    ],

    // Proxy configuration from config
    ...(config.proxies.enabled && {
      proxyConfiguration: new ProxyConfiguration({
        proxyUrls: config.proxies.urls
      })
    }),

    // Session pool for cookie management and retry logic
    sessionPoolOptions: {
      maxPoolSize: config.sessions.maxPoolSize,
      sessionOptions: {
        maxErrorScore: config.sessions.maxErrorScore,
        maxUsageCount: config.sessions.maxUsageCount
      },
    },

    // Error handling for blocked requests will be done in requestHandler

    launchContext: {
      launchOptions: {
        headless: config.crawler.headless,
        args: config.browserArgs
      }
    },
    maxConcurrency: config.crawler.maxConcurrency,

    requestHandler: async ({ page, log, session }) => {
      log.info(`Visiting ${page.url()} with session ${session.id}`);

      // Set random user agent and headers for each request
      const userAgent = getRandomUserAgent();
      const referer = getRandomReferer();

      // Set user agent using context method
      await page.context().addInitScript(`
        Object.defineProperty(navigator, 'userAgent', {
          get: () => '${userAgent}'
        });
      `);

      await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
        'Referer': referer
      });

      log.info(`Using User-Agent: ${userAgent.substring(0, 50)}...`);

      // Set realistic viewport
      await page.setViewportSize({
        width: 1366 + Math.floor(Math.random() * 200),
        height: 768 + Math.floor(Math.random() * 200)
      });

      // Add random delay to mimic human behavior
      const delay = getRandomDelay();
      await page.waitForTimeout(delay);

      // Simulate human mouse movement before interacting
      await page.mouse.move(
        Math.random() * 100 + 50,
        Math.random() * 100 + 50,
        { steps: 10 }
      );

      const challengeSolved = await solveHumanChallenge(page, log);
      if (!challengeSolved) {
        log.error('Failed to solve human challenge, aborting this request');
        throw new Error('Human challenge not solved');
      }

      // wait for listings to render with timeout
      try {
        await page.waitForLoadState('networkidle', { timeout: 30000 });
      } catch (error) {
        log.warning('Page did not reach networkidle state, continuing anyway');
      }

      // Simulate human scrolling behavior
      await page.evaluate(() => {
        window.scrollTo(0, Math.random() * 500);
      });
      await page.waitForTimeout(1000 + Math.random() * 2000);

      // Scroll back to top
      await page.evaluate(() => {
        window.scrollTo(0, 0);
      });
      await page.waitForTimeout(500);

      // Check if we got blocked (common blocking indicators)
      const pageContent = await page.content();
      const isBlocked = config.blockingKeywords.some(keyword =>
        pageContent.includes(keyword)
      );

      if (isBlocked) {
        log.warning('Detected blocking page, will retry with longer delay');
        // Don't retire session immediately, just wait longer
        await page.waitForTimeout(10000 + Math.random() * 5000); // 10-15 second delay
        // Let the crawler retry naturally
        throw new Error('Page blocked - retrying with delay');
      }

      // Add debugging to see page structure
      const pageInfo = await page.evaluate(() => {
        return {
          title: document.title,
          url: window.location.href,
          h1Elements: Array.from(document.querySelectorAll('h1')).map(el => el.innerText.trim()),
          h2Elements: Array.from(document.querySelectorAll('h2')).map(el => el.innerText.trim()),
          dataTestIds: Array.from(document.querySelectorAll('[data-testid]')).map(el => el.getAttribute('data-testid')),
          totalElements: document.querySelectorAll('*').length
        };
      });

      log.info(`Page info: ${JSON.stringify(pageInfo, null, 2)}`);

      const data = await page.evaluate(() => {
        // Helper function to safely get text content
        const getText = (selector) => {
          const el = document.querySelector(selector);
          return el ? el.innerText.trim() : '';
        };

        // Helper function to get all text contents
        const getAllText = (selector) => {
          const elements = document.querySelectorAll(selector);
          return Array.from(elements).map(el => el.innerText.trim()).filter(text => text);
        };

        // Basic profile information
        const name = getText('h1') || getText('[data-testid="agent-name"]') || getText('.agent-name');

        // Profile photo
        const photoEl = document.querySelector('img[alt*="Profile"]') ||
                       document.querySelector('[data-testid="agent-photo"] img') ||
                       document.querySelector('.agent-photo img');
        const photo = photoEl?.src || '';

        // Contact information
        const phone = getText('[data-testid="agent-phone"]') ||
                     getText('a[href^="tel:"]') ||
                     getText('.phone-number');

        // Company/brokerage information
        const company = getText('[data-testid="agent-company"]') ||
                       getText('.brokerage-name') ||
                       getText('.company-name');

        // Bio/description
        const bio = getText('[data-testid="agent-bio"]') ||
                   getText('.agent-bio') ||
                   getText('.description');

        // Reviews and ratings
        const rating = getText('[data-testid="agent-rating"]') ||
                      getText('.rating') ||
                      getText('.star-rating');

        const reviewCount = getText('[data-testid="review-count"]') ||
                           getText('.review-count');

        // Recent sales/listings
        const listings = [];

        // Try multiple selectors for listings
        const listingSelectors = [
          '[data-testid="property-card"]',
          '.property-card',
          '.listing-card',
          '.property-listing',
          'article[data-testid*="property"]'
        ];

        let listingElements = [];
        for (const selector of listingSelectors) {
          listingElements = document.querySelectorAll(selector);
          if (listingElements.length > 0) break;
        }

        Array.from(listingElements).forEach((card, index) => {
          try {
            const linkEl = card.querySelector('a[href*="/homedetails/"]') ||
                          card.querySelector('a[href*="/b/"]') ||
                          card.querySelector('a');

            const url = linkEl?.href || '';

            const addressEl = card.querySelector('[data-testid="property-address"]') ||
                             card.querySelector('.address') ||
                             card.querySelector('.property-address');
            const address = addressEl?.innerText.trim() || '';

            const priceEl = card.querySelector('[data-testid="property-price"]') ||
                           card.querySelector('.price') ||
                           card.querySelector('.property-price');
            const price = priceEl?.innerText.trim() || '';

            const bedsEl = card.querySelector('[data-testid="property-beds"]') ||
                          card.querySelector('.beds');
            const beds = bedsEl?.innerText.trim() || '';

            const bathsEl = card.querySelector('[data-testid="property-baths"]') ||
                           card.querySelector('.baths');
            const baths = bathsEl?.innerText.trim() || '';

            const sqftEl = card.querySelector('[data-testid="property-sqft"]') ||
                          card.querySelector('.sqft');
            const sqft = sqftEl?.innerText.trim() || '';

            const statusEl = card.querySelector('[data-testid="property-status"]') ||
                            card.querySelector('.status');
            const status = statusEl?.innerText.trim() || '';

            if (url || address || price) {
              listings.push({
                url,
                address,
                price,
                beds,
                baths,
                sqft,
                status,
                index: index + 1
              });
            }
          } catch (e) {
            console.log('Error extracting listing:', e);
          }
        });

        // Additional profile data
        const specialties = getAllText('[data-testid="agent-specialty"]') ||
                           getAllText('.specialty') ||
                           getAllText('.specialization');

        const languages = getAllText('[data-testid="agent-language"]') ||
                         getAllText('.language');

        const experience = getText('[data-testid="agent-experience"]') ||
                          getText('.experience') ||
                          getText('.years-experience');

        // Social media links
        const socialLinks = {};
        const socialSelectors = {
          facebook: 'a[href*="facebook.com"]',
          twitter: 'a[href*="twitter.com"]',
          linkedin: 'a[href*="linkedin.com"]',
          instagram: 'a[href*="instagram.com"]'
        };

        Object.entries(socialSelectors).forEach(([platform, selector]) => {
          const link = document.querySelector(selector);
          if (link) socialLinks[platform] = link.href;
        });

        // Get page URL and timestamp
        const pageUrl = window.location.href;
        const scrapedAt = new Date().toISOString();

        return {
          name,
          photo,
          phone,
          company,
          bio,
          rating,
          reviewCount,
          specialties,
          languages,
          experience,
          socialLinks,
          listings,
          pageUrl,
          scrapedAt,
          totalListings: listings.length
        };
      });

      console.log(JSON.stringify(data, null, 2));

      // Save data to file
      const fs = await import('node:fs');
      const filename = `zillow_profile_${Date.now()}.json`;
      await fs.promises.writeFile(filename, JSON.stringify(data, null, 2));
      log.info(`Data saved to ${filename}`);
    }
  });

  await crawler.run(['https://www.zillow.com/profile/mccannteam']);
})();
