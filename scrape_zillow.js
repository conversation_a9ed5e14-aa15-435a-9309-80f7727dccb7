// scrape_zillow.js
import { PlaywrightCrawler, ProxyConfiguration } from 'crawlee';
import { addExtra } from 'playwright-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import playwright from 'playwright';
import { config } from './scraper-config.js';

// Initialize playwright-extra with stealth plugin
const pw = addExtra(playwright);
pw.use(StealthPlugin());

function getRandomUserAgent() {
  return config.userAgents[Math.floor(Math.random() * config.userAgents.length)];
}

function getRandomReferer() {
  return config.referers[Math.floor(Math.random() * config.referers.length)];
}

function getRandomDelay() {
  return config.crawler.requestDelay.min +
         Math.random() * (config.crawler.requestDelay.max - config.crawler.requestDelay.min);
}

async function solveHumanChallenge(page, log) {
  // Try multiple times to find and solve the challenge
  for (let attempt = 0; attempt < 5; attempt++) {
    // Wait a bit before checking for challenge
    await page.waitForTimeout(1000 + Math.random() * 2000);

    const btn = await page.$('text=Press and Hold');
    if (!btn) {
      if (attempt === 0) {
        log.info('No human challenge detected on this attempt');
      }
      return true; // No challenge found, success
    }

    const box = await btn.boundingBox();
    if (!box) {
      log.warning('Challenge button found but no bounding box');
      continue;
    }

    const cx = box.x + box.width/2;
    const cy = box.y + box.height/2;

    log.info(`HUMAN challenge detected – solving… (attempt ${attempt + 1})`);

    // More human-like approach to the button
    await page.mouse.move(
      cx + (Math.random() - 0.5) * 20,
      cy + (Math.random() - 0.5) * 20,
      { steps: 15 + Math.floor(Math.random() * 10) }
    );

    // Pause before clicking like a human would
    await page.waitForTimeout(500 + Math.random() * 1000);

    // Move to exact center more naturally
    await page.mouse.move(cx, cy, { steps: 5 });
    await page.waitForTimeout(200 + Math.random() * 300);

    await page.mouse.down();
    log.info('Holding button...');

    // Much longer hold time with more variation
    const holdTime = 6000 + Math.random() * 3000; // 6-9 seconds
    const start = Date.now();
    let moveCount = 0;

    while (Date.now() - start < holdTime) {
      // More realistic micro-movements
      if (moveCount % 10 === 0) { // Move less frequently
        await page.mouse.move(
          cx + (Math.random() - 0.5) * 2, // Smaller movements
          cy + (Math.random() - 0.5) * 2,
          { steps: 1 }
        );
      }
      moveCount++;
      await page.waitForTimeout(150 + Math.random() * 100);
    }

    await page.mouse.up();
    log.info('Released button, waiting for verification...');

    // Wait longer for the challenge to process
    await page.waitForTimeout(3000 + Math.random() * 2000);

    // Check if challenge was solved by looking for page changes
    try {
      // Wait for either the challenge to disappear or page to change
      await Promise.race([
        page.waitForSelector('text=Press and Hold', { state: 'detached', timeout: 5000 }),
        page.waitForLoadState('networkidle', { timeout: 5000 }),
        page.waitForTimeout(5000)
      ]);
    } catch (e) {
      log.info('Timeout waiting for challenge resolution');
    }

    // Check if challenge is still present
    const stillPresent = await page.$('text=Press and Hold');
    if (!stillPresent) {
      log.info('✅ Human challenge solved successfully!');
      return true;
    }

    log.warning(`❌ Challenge still present after attempt ${attempt + 1}, retrying...`);

    // Wait longer between attempts
    await page.waitForTimeout(3000 + Math.random() * 2000);

    // Try refreshing the page on later attempts
    if (attempt >= 2) {
      log.info('Refreshing page to get new challenge...');
      await page.reload({ waitUntil: 'networkidle' });
      await page.waitForTimeout(3000);
    }
  }

  log.error('Failed to solve human challenge after 5 attempts');
  return false;
}

(async () => {
  const crawler = new PlaywrightCrawler({
    // Browser pool configuration to reuse same browser
    browserPoolOptions: {
      maxOpenPagesPerBrowser: 1,
      retireBrowserAfterPageCount: 100, // Keep browser alive longer
    },

    // Use stealth browser with pre-launch hook
    preNavigationHooks: [
      async ({ page }) => {
        // Apply comprehensive stealth techniques
        await page.addInitScript(() => {
          // Remove webdriver property
          Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
          });

          // Mock chrome property
          window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
          };

          // Mock plugins with realistic data
          Object.defineProperty(navigator, 'plugins', {
            get: () => ({
              length: 3,
              0: { name: 'Chrome PDF Plugin' },
              1: { name: 'Chrome PDF Viewer' },
              2: { name: 'Native Client' }
            }),
          });

          // Mock languages
          Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
          });

          // Mock permissions
          const originalQuery = window.navigator.permissions.query;
          window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
              Promise.resolve({ state: Notification.permission }) :
              originalQuery(parameters)
          );

          // Hide automation indicators
          Object.defineProperty(navigator, 'platform', {
            get: () => 'Win32',
          });

          // Mock screen properties
          Object.defineProperty(screen, 'width', { get: () => 1920 });
          Object.defineProperty(screen, 'height', { get: () => 1080 });
          Object.defineProperty(screen, 'availWidth', { get: () => 1920 });
          Object.defineProperty(screen, 'availHeight', { get: () => 1040 });
        });
      }
    ],

    // Proxy configuration from config
    ...(config.proxies.enabled && {
      proxyConfiguration: new ProxyConfiguration({
        proxyUrls: config.proxies.urls
      })
    }),

    // Session pool for cookie management and retry logic
    sessionPoolOptions: {
      maxPoolSize: config.sessions.maxPoolSize,
      sessionOptions: {
        maxErrorScore: config.sessions.maxErrorScore,
        maxUsageCount: config.sessions.maxUsageCount
      },
    },

    // Error handling for blocked requests will be done in requestHandler

    launchContext: {
      launchOptions: {
        headless: config.crawler.headless,
        args: config.browserArgs
      }
    },
    maxConcurrency: config.crawler.maxConcurrency,

    requestHandler: async ({ page, log, session }) => {
      log.info(`Visiting ${page.url()} with session ${session.id}`);

      // Set random user agent and headers for each request
      const userAgent = getRandomUserAgent();
      const referer = getRandomReferer();

      // Set user agent using context method
      await page.context().addInitScript(`
        Object.defineProperty(navigator, 'userAgent', {
          get: () => '${userAgent}'
        });
      `);

      await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
        'Referer': referer
      });

      log.info(`Using User-Agent: ${userAgent.substring(0, 50)}...`);

      // Set realistic viewport
      await page.setViewportSize({
        width: 1366 + Math.floor(Math.random() * 200),
        height: 768 + Math.floor(Math.random() * 200)
      });

      // Add random delay to mimic human behavior
      const delay = getRandomDelay();
      await page.waitForTimeout(delay);

      // Simulate human mouse movement before interacting
      await page.mouse.move(
        Math.random() * 100 + 50,
        Math.random() * 100 + 50,
        { steps: 10 }
      );

      const challengeSolved = await solveHumanChallenge(page, log);
      if (!challengeSolved) {
        log.error('Failed to solve human challenge, aborting this request');
        throw new Error('Human challenge not solved');
      }

      // wait for listings to render with timeout
      try {
        await page.waitForLoadState('networkidle', { timeout: 30000 });
      } catch (error) {
        log.warning('Page did not reach networkidle state, continuing anyway');
      }

      // Simulate human scrolling behavior
      await page.evaluate(() => {
        window.scrollTo(0, Math.random() * 500);
      });
      await page.waitForTimeout(1000 + Math.random() * 2000);

      // Scroll back to top
      await page.evaluate(() => {
        window.scrollTo(0, 0);
      });
      await page.waitForTimeout(500);

      // Check if we got blocked (common blocking indicators)
      const pageContent = await page.content();
      const isBlocked = config.blockingKeywords.some(keyword =>
        pageContent.includes(keyword)
      );

      if (isBlocked) {
        log.warning('Detected blocking page, will retry with longer delay');
        // Don't retire session immediately, just wait longer
        await page.waitForTimeout(10000 + Math.random() * 5000); // 10-15 second delay
        // Let the crawler retry naturally
        throw new Error('Page blocked - retrying with delay');
      }

      // Add debugging to see page structure
      const pageInfo = await page.evaluate(() => {
        return {
          title: document.title,
          url: window.location.href,
          h1Elements: Array.from(document.querySelectorAll('h1')).map(el => el.innerText.trim()),
          h2Elements: Array.from(document.querySelectorAll('h2')).map(el => el.innerText.trim()),
          dataTestIds: Array.from(document.querySelectorAll('[data-testid]')).map(el => el.getAttribute('data-testid')),
          totalElements: document.querySelectorAll('*').length
        };
      });

      log.info(`Page info: ${JSON.stringify(pageInfo, null, 2)}`);

      const data = await page.evaluate(() => {
        // Helper function to safely extract nested properties
        const safeGet = (obj, path, defaultValue = null) => {
          try {
            return path.split('.').reduce((current, key) => current && current[key], obj) || defaultValue;
          } catch (e) {
            return defaultValue;
          }
        };

        // Helper function to safely get text content
        const getText = (selector) => {
          const el = document.querySelector(selector);
          return el ? el.innerText.trim() : '';
        };

        const getAttribute = (selector, attr) => {
          const el = document.querySelector(selector);
          return el ? el.getAttribute(attr) : '';
        };

        // Try to find Zillow's internal data structures
        let zillowData = {};

        // Method 1: Check for __NEXT_DATA__ (Next.js data)
        const nextDataScript = document.querySelector('#__NEXT_DATA__');
        if (nextDataScript) {
          try {
            const nextData = JSON.parse(nextDataScript.textContent);
            zillowData = nextData.props?.pageProps || nextData.props?.initialProps || {};
          } catch (e) {
            console.log('Could not parse __NEXT_DATA__');
          }
        }

        // Method 2: Check for window data structures
        if (window.__INITIAL_STATE__) {
          zillowData = { ...zillowData, ...window.__INITIAL_STATE__ };
        }
        if (window.zpData) {
          zillowData = { ...zillowData, ...window.zpData };
        }

        // Method 3: Look for JSON-LD structured data
        const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
        let structuredData = {};
        jsonLdScripts.forEach(script => {
          try {
            const data = JSON.parse(script.textContent);
            if (data['@type'] === 'RealEstateAgent' || data['@type'] === 'Person') {
              structuredData = { ...structuredData, ...data };
            }
          } catch (e) {
            // Ignore parsing errors
          }
        });

        // Extract agent data from various possible locations
        // Based on debug output, the data is directly in zillowData
        const agentData = zillowData; // Use the entire zillowData object

        // Also try to get displayUser data which might contain agent info
        const displayUser = safeGet(zillowData, 'displayUser', {});

        // Build comprehensive response matching your requirements
        const result = {
          // Basic profile information
          url: window.location.href,
          encodedZuid: safeGet(displayUser, 'encodedZuid') || safeGet(agentData, 'zGuid'),
          screenName: safeGet(displayUser, 'screenName') || getText('h1'),
          inCanada: safeGet(displayUser, 'inCanada', false),
          name: safeGet(displayUser, 'name') || safeGet(structuredData, 'name') || getText('h1'),
          flag: safeGet(displayUser, 'flag'),
          profileTypeIds: safeGet(displayUser, 'profileTypeIds', []),
          profileTypes: safeGet(displayUser, 'profileTypes', []),
          sidebarVideoUrl: safeGet(agentData, 'sidebarVideoUrl'),

          // Business information
          businessAddress: {
            address1: safeGet(displayUser, 'businessAddress.address1'),
            address2: safeGet(displayUser, 'businessAddress.address2'),
            city: safeGet(displayUser, 'businessAddress.city'),
            state: safeGet(displayUser, 'businessAddress.state'),
            postalCode: safeGet(displayUser, 'businessAddress.postalCode')
          },
          businessName: safeGet(displayUser, 'businessName') || getText('[data-testid="agent-company"]'),
          cpdUserPronouns: safeGet(displayUser, 'cpdUserPronouns'),

          // Agent status
          isTopAgent: safeGet(displayUser, 'isTopAgent', false),
          isPremierAgent: safeGet(displayUser, 'isPremierAgent', false),

          // Profile media
          profileImageId: safeGet(displayUser, 'profileImageId'),
          profilePhotoSrc: safeGet(displayUser, 'profilePhotoSrc') || getAttribute('img[alt*="Profile"]', 'src'),

          // Ratings and reviews
          ratings: {
            totalCount: safeGet(agentData, 'reviewsData.totalCount', 0),
            averageRating: safeGet(agentData, 'reviewsData.averageRating', 0)
          },

          // Contact information
          phoneNumbers: {
            cell: safeGet(displayUser, 'phoneNumbers.cell'),
            brokerage: safeGet(displayUser, 'phoneNumbers.brokerage'),
            business: safeGet(displayUser, 'phoneNumbers.business') || getText('a[href^="tel:"]').replace(/[^\d]/g, '')
          },
          email: safeGet(displayUser, 'email') || safeGet(structuredData, 'email'),

          // Professional information
          professional: safeGet(agentData, 'professional', {}),
          getToKnowMe: safeGet(agentData, 'getToKnowMe', {}),
          agentLicenses: safeGet(agentData, 'agentLicenses', []),

          // Sales statistics
          agentSalesStats: {
            countAllTime: safeGet(agentData, 'agentSalesStats.countAllTime', 0),
            countLastYear: safeGet(agentData, 'agentSalesStats.countLastYear', 0),
            priceRangeThreeYearMin: safeGet(agentData, 'agentSalesStats.priceRangeThreeYearMin'),
            priceRangeThreeYearMax: safeGet(agentData, 'agentSalesStats.priceRangeThreeYearMax'),
            averageValueThreeYear: safeGet(agentData, 'agentSalesStats.averageValueThreeYear'),
            stats_include_team: safeGet(agentData, 'agentSalesStats.stats_include_team', false)
          },

          // Listings data - extract from DOM as fallback
          forSaleListings: safeGet(agentData, 'forSaleListings', []).map(listing => ({
            zpid: safeGet(listing, 'zpid'),
            home_type: safeGet(listing, 'home_type'),
            address: safeGet(listing, 'address'),
            bedrooms: safeGet(listing, 'bedrooms'),
            bathrooms: safeGet(listing, 'bathrooms'),
            price: safeGet(listing, 'price'),
            latitude: safeGet(listing, 'latitude'),
            longitude: safeGet(listing, 'longitude'),
            brokerage_name: safeGet(listing, 'brokerage_name'),
            home_marketing_status: safeGet(listing, 'home_marketing_status'),
            listing_url: safeGet(listing, 'listing_url')
          })),

          forRentListings: safeGet(agentData, 'forRentListings', []).map(listing => ({
            zpid: safeGet(listing, 'zpid'),
            home_type: safeGet(listing, 'home_type'),
            address: safeGet(listing, 'address'),
            bedrooms: safeGet(listing, 'bedrooms'),
            bathrooms: safeGet(listing, 'bathrooms'),
            price: safeGet(listing, 'price'),
            latitude: safeGet(listing, 'latitude'),
            longitude: safeGet(listing, 'longitude'),
            brokerage_name: safeGet(listing, 'brokerage_name'),
            home_marketing_status: safeGet(listing, 'home_marketing_status'),
            listing_url: safeGet(listing, 'listing_url')
          })),

          // Past sales
          pastSales: {
            totalSales: safeGet(agentData, 'pastSales.totalSales', 0),
            past_sales: safeGet(agentData, 'pastSales.past_sales', []).map(sale => ({
              represented: safeGet(sale, 'represented'),
              sold_date: safeGet(sale, 'sold_date'),
              price: safeGet(sale, 'price'),
              street_address: safeGet(sale, 'street_address'),
              city_state_zipcode: safeGet(sale, 'city_state_zipcode'),
              latitude: safeGet(sale, 'latitude'),
              longitude: safeGet(sale, 'longitude'),
              bathrooms: safeGet(sale, 'bathrooms'),
              bedrooms: safeGet(sale, 'bedrooms'),
              city: safeGet(sale, 'city'),
              state: safeGet(sale, 'state'),
              livingAreaValue: safeGet(sale, 'livingAreaValue'),
              livingAreaUnitsShort: safeGet(sale, 'livingAreaUnitsShort')
            }))
          },

          // Additional professional data
          preferredLenders: safeGet(agentData, 'preferredLenders', {}),
          professionalInformation: safeGet(agentData, 'professionalInformation', []),

          // Reviews data
          reviewsData: {
            reviews: safeGet(agentData, 'reviewsData.reviews', []).map(review => ({
              reviewComment: safeGet(review, 'reviewComment'),
              reviewId: safeGet(review, 'reviewId'),
              rating: safeGet(review, 'rating'),
              createDate: safeGet(review, 'createDate')
            })),
            subRatings: safeGet(agentData, 'reviewsData.subRatings', {}),
            reviewee: safeGet(agentData, 'reviewsData.reviewee', {})
          },

          // Team information
          teamDisplayInformation: {
            teamLeadInfo: safeGet(agentData, 'teamDisplayInformation.teamLeadInfo', {}),
            teamMemberInfo: safeGet(agentData, 'teamDisplayInformation.teamMemberInfo', [])
          },

          // Metadata
          scrapedAt: new Date().toISOString(),
          pageTitle: document.title,

          // Debug information to help understand data sources
          _debug: {
            foundDataSources: {
              nextData: !!nextDataScript,
              initialState: !!window.__INITIAL_STATE__,
              zpData: !!window.zpData,
              structuredData: Object.keys(structuredData).length > 0
            },
            availableKeys: Object.keys(zillowData),
            agentDataKeys: Object.keys(agentData)
          }
        };

        return result;
      });

      console.log(JSON.stringify(data, null, 2));

      // Save data to file
      const fs = await import('node:fs');
      const filename = `zillow_profile_${Date.now()}.json`;
      await fs.promises.writeFile(filename, JSON.stringify(data, null, 2));
      log.info(`Data saved to ${filename}`);
    }
  });

  await crawler.run(['https://www.zillow.com/profile/mccannteam']);
})();
