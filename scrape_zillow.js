// scrape_zillow.js
import { Playwright<PERSON>rawler } from 'crawlee';
import { addExtra } from 'playwright-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import playwright from 'playwright';
import { config } from './scraper-config.js';

// Initialize playwright-extra with stealth plugin
const pw = addExtra(playwright);
pw.use(StealthPlugin());

function getRandomUserAgent() {
  return config.userAgents[Math.floor(Math.random() * config.userAgents.length)];
}

function getRandomReferer() {
  return config.referers[Math.floor(Math.random() * config.referers.length)];
}

function getRandomDelay() {
  return config.crawler.requestDelay.min +
         Math.random() * (config.crawler.requestDelay.max - config.crawler.requestDelay.min);
}

async function solveHumanChallenge(page, log) {
  // Try multiple times to find and solve the challenge
  for (let attempt = 0; attempt < 3; attempt++) {
    const btn = await page.$('text=Press and Hold');
    if (!btn) {
      if (attempt === 0) {
        log.info('No human challenge detected on this attempt');
      }
      return;
    }

    const box = await btn.boundingBox();
    if (!box) continue;

    const cx = box.x + box.width/2;
    const cy = box.y + box.height/2;

    log.info(`HUMAN challenge detected – holding… (attempt ${attempt + 1})`);
    await page.mouse.move(cx, cy, { steps: 10 });
    await page.mouse.down();

    const holdTime = 4500 + Math.random()*1000; // Slightly longer hold
    const start = Date.now();
    while (Date.now() - start < holdTime) {
      await page.mouse.move(
        cx + (Math.random()-0.5)*4,
        cy + (Math.random()-0.5)*4,
        { steps: 2 }
      );
      await page.waitForTimeout(100 + Math.random()*50);
    }

    await page.mouse.up();
    await page.waitForTimeout(2000); // Wait longer after solving

    // Check if challenge was solved
    const stillPresent = await page.$('text=Press and Hold');
    if (!stillPresent) {
      log.info('Human challenge solved successfully!');
      return;
    }

    log.warning(`Challenge still present after attempt ${attempt + 1}, retrying...`);
    await page.waitForTimeout(1000);
  }
}

(async () => {
  const crawler = new PlaywrightCrawler({
    // Browser pool configuration to reuse same browser
    browserPoolOptions: {
      maxOpenPagesPerBrowser: 1,
      retireBrowserAfterPageCount: 100, // Keep browser alive longer
    },

    // Use stealth browser with pre-launch hook
    preNavigationHooks: [
      async ({ page }) => {
        // Apply comprehensive stealth techniques
        await page.addInitScript(() => {
          // Remove webdriver property
          Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
          });

          // Mock chrome property
          window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
          };

          // Mock plugins with realistic data
          Object.defineProperty(navigator, 'plugins', {
            get: () => ({
              length: 3,
              0: { name: 'Chrome PDF Plugin' },
              1: { name: 'Chrome PDF Viewer' },
              2: { name: 'Native Client' }
            }),
          });

          // Mock languages
          Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
          });

          // Mock permissions
          const originalQuery = window.navigator.permissions.query;
          window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
              Promise.resolve({ state: Notification.permission }) :
              originalQuery(parameters)
          );

          // Hide automation indicators
          Object.defineProperty(navigator, 'platform', {
            get: () => 'Win32',
          });

          // Mock screen properties
          Object.defineProperty(screen, 'width', { get: () => 1920 });
          Object.defineProperty(screen, 'height', { get: () => 1080 });
          Object.defineProperty(screen, 'availWidth', { get: () => 1920 });
          Object.defineProperty(screen, 'availHeight', { get: () => 1040 });
        });
      }
    ],

    // Proxy configuration from config
    ...(config.proxies.enabled && {
      proxyConfiguration: {
        proxyUrls: config.proxies.urls
      }
    }),

    // Session pool for cookie management and retry logic
    sessionPoolOptions: {
      maxPoolSize: config.sessions.maxPoolSize,
      sessionOptions: {
        maxErrorScore: config.sessions.maxErrorScore,
        maxUsageCount: config.sessions.maxUsageCount
      },
    },

    // Error handling for blocked requests will be done in requestHandler

    launchContext: {
      launchOptions: {
        headless: config.crawler.headless,
        args: config.browserArgs
      }
    },
    maxConcurrency: config.crawler.maxConcurrency,

    requestHandler: async ({ page, log, session }) => {
      log.info(`Visiting ${page.url()} with session ${session.id}`);

      // Set random user agent and headers for each request
      const userAgent = getRandomUserAgent();
      const referer = getRandomReferer();

      // Set user agent using context method
      await page.context().addInitScript(`
        Object.defineProperty(navigator, 'userAgent', {
          get: () => '${userAgent}'
        });
      `);

      await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
        'Referer': referer
      });

      log.info(`Using User-Agent: ${userAgent.substring(0, 50)}...`);

      // Set realistic viewport
      await page.setViewportSize({
        width: 1366 + Math.floor(Math.random() * 200),
        height: 768 + Math.floor(Math.random() * 200)
      });

      // Add random delay to mimic human behavior
      const delay = getRandomDelay();
      await page.waitForTimeout(delay);

      // Simulate human mouse movement before interacting
      await page.mouse.move(
        Math.random() * 100 + 50,
        Math.random() * 100 + 50,
        { steps: 10 }
      );

      await solveHumanChallenge(page, log);

      // wait for listings to render with timeout
      try {
        await page.waitForLoadState('networkidle', { timeout: 30000 });
      } catch (error) {
        log.warning('Page did not reach networkidle state, continuing anyway');
      }

      // Simulate human scrolling behavior
      await page.evaluate(() => {
        window.scrollTo(0, Math.random() * 500);
      });
      await page.waitForTimeout(1000 + Math.random() * 2000);

      // Scroll back to top
      await page.evaluate(() => {
        window.scrollTo(0, 0);
      });
      await page.waitForTimeout(500);

      // Check if we got blocked (common blocking indicators)
      const pageContent = await page.content();
      const isBlocked = config.blockingKeywords.some(keyword =>
        pageContent.includes(keyword)
      );

      if (isBlocked) {
        log.warning('Detected blocking page, will retry with longer delay');
        // Don't retire session immediately, just wait longer
        await page.waitForTimeout(10000 + Math.random() * 5000); // 10-15 second delay
        // Let the crawler retry naturally
        throw new Error('Page blocked - retrying with delay');
      }

      const data = await page.evaluate(() => {
        // name
        const name = document.querySelector('h1')?.innerText.trim() || '';

        // profile photo (if any)
        const photoEl = document.querySelector('img[alt*="Profile"]');
        const photo = photoEl?.src || '';

        // For Sale listings
        const header = Array.from(document.querySelectorAll('h2'))
          .find(h2 => h2.textContent.includes('For Sale'));
        const listings = [];
        if (header) {
          let el = header.nextElementSibling;
          while (el && el.tagName !== 'H2') {
            if (el.tagName === 'A') {
              const href    = el.href;
              const txt     = el.innerText.trim();
              const price   = el.nextSibling?.textContent.trim() || '';
              const [addr, rest] = txt.split(' Bed/Bath: ')
              const match  = rest?.match(/(\d+)\s*Bed,\s*(\d+)\s*Bath/);
              const bedrooms = match ? +match[1] : null;
              const bathrooms= match ? +match[2] : null;

              listings.push({ url: href, address: addr.trim(),
                               bedrooms, bathrooms, price });
            }
            el = el.nextElementSibling;
          }
        }

        return { name, photo, listings };
      });

      console.log(JSON.stringify(data, null, 2));
    }
  });

  await crawler.run(['https://www.zillow.com/profile/mccannteam']);
})();
