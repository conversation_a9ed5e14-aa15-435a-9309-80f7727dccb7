// scrape_zillow.js
import { PlaywrightCrawler } from 'crawlee';

async function solveHumanChallenge(page, log) {
  const btn = await page.$('text=Press and Hold');
  if (!btn) return;
  const box = await btn.boundingBox();
  if (!box) return;
  const cx = box.x + box.width/2, cy = box.y + box.height/2;

  log.info('HUMAN challenge detected – holding…');
  await page.mouse.move(cx, cy, { steps: 10 });
  await page.mouse.down();

  const holdTime = 4500 + Math.random()*500;
  const start = Date.now();
  while (Date.now() - start < holdTime) {
    await page.mouse.move(
      cx + (Math.random()-0.5)*4,
      cy + (Math.random()-0.5)*4,
      { steps: 2 }
    );
    await page.waitForTimeout(100 + Math.random()*50);
  }

  await page.mouse.up();
  await page.waitForTimeout(1000);
}

(async () => {
  const crawler = new PlaywrightCrawler({
    launchContext: { launchOptions: { headless: true } },
    maxConcurrency: 2,
    requestHandler: async ({ page, log }) => {
      log.info(`Visiting ${page.url()}`);
      await solveHumanChallenge(page, log);
      // wait for listings to render
      await page.waitForLoadState('networkidle');

      const data = await page.evaluate(() => {
        // name
        const name = document.querySelector('h1')?.innerText.trim() || '';

        // profile photo (if any)
        const photoEl = document.querySelector('img[alt*="Profile"]');
        const photo = photoEl?.src || '';

        // For Sale listings
        const header = Array.from(document.querySelectorAll('h2'))
          .find(h2 => h2.textContent.includes('For Sale'));
        const listings = [];
        if (header) {
          let el = header.nextElementSibling;
          while (el && el.tagName !== 'H2') {
            if (el.tagName === 'A') {
              const href    = el.href;
              const txt     = el.innerText.trim();  
              const price   = el.nextSibling?.textContent.trim() || '';
              const [addr, rest] = txt.split(' Bed/Bath: ')  
              const match  = rest?.match(/(\d+)\s*Bed,\s*(\d+)\s*Bath/);
              const bedrooms = match ? +match[1] : null;
              const bathrooms= match ? +match[2] : null;

              listings.push({ url: href, address: addr.trim(),
                               bedrooms, bathrooms, price });
            }
            el = el.nextElementSibling;
          }
        }

        return { name, photo, listings };
      });

      console.log(JSON.stringify(data, null, 2));
    }
  });

  await crawler.run(['https://www.zillow.com/profile/mccannteam']);
})();
