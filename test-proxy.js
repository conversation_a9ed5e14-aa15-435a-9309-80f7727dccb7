// test-proxy.js
// Simple test to verify Oxylabs proxy connection

import playwright from 'playwright';

async function testProxy() {
  console.log('Testing Oxylabs proxy connection...');

  const browser = await playwright.chromium.launch({
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  // Try different proxy configurations
  const proxyConfigs = [
    {
      name: 'Unencoded password',
      proxy: {
        server: 'http://pr.oxylabs.io:7777',
        username: 'MVPLE<PERSON>',
        password: 'p5xUcKMH7hJ4f29+'
      }
    },
    {
      name: 'URL encoded password',
      proxy: {
        server: 'http://pr.oxylabs.io:7777',
        username: 'MVP<PERSON><PERSON>',
        password: 'p5xUcKMH7hJ4f29%2B'
      }
    },
    {
      name: 'Different endpoint',
      proxy: {
        server: 'http://residential.oxylabs.io:8001',
        username: '<PERSON><PERSON><PERSON>',
        password: 'p5xUcKMH7hJ4f29+'
      }
    }
  ];

  for (const config of proxyConfigs) {
    console.log(`\nTesting: ${config.name}`);
    console.log(`Server: ${config.proxy.server}`);

    try {
      const context = await browser.newContext(config);
      const page = await context.newPage();

      console.log('Attempting to connect through proxy...');

      // Test with a simple site first
      const response = await page.goto('https://httpbin.org/ip', {
        waitUntil: 'domcontentloaded',
        timeout: 15000
      });

      console.log(`✅ Response status: ${response.status()}`);

      if (response.status() === 200) {
        console.log('✅ Proxy connection successful!');

        // Get the IP to verify proxy is working
        const ipInfo = await page.evaluate(() => {
          try {
            return JSON.parse(document.body.innerText);
          } catch (e) {
            return { error: 'Could not parse response' };
          }
        });

        console.log('✅ Your IP through proxy:', ipInfo);
        await context.close();
        break; // Success! Stop testing other configs
      } else {
        console.log('❌ Failed to connect through proxy');
      }

      await context.close();

    } catch (error) {
      console.error(`❌ Proxy test failed for ${config.name}:`, error.message);
    }
  }

  // Try without proxy to compare
  console.log('\n--- Testing direct connection for comparison ---');
  try {
    const directContext = await browser.newContext();
    const directPage = await directContext.newPage();

    const directResponse = await directPage.goto('https://httpbin.org/ip', {
      waitUntil: 'domcontentloaded',
      timeout: 10000
    });

    console.log(`Direct connection status: ${directResponse.status()}`);

    if (directResponse.status() === 200) {
      const directIp = await directPage.evaluate(() => {
        try {
          return JSON.parse(document.body.innerText);
        } catch (e) {
          return { error: 'Could not parse response' };
        }
      });
      console.log('Your direct IP:', directIp);
    }

    await directContext.close();
  } catch (directError) {
    console.error('Direct connection also failed:', directError.message);
  }

  await browser.close();
}

await testProxy();
